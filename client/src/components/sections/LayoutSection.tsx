import { motion } from 'framer-motion';
import {
  AlertTriangle,
  Clock,
  DollarSign,
  Puzzle,
  Shield,
  TrendingDown,
  Users,
} from 'lucide-react';

export const LayoutSection = () => {
  const problemCards = [
    {
      title: 'Cash Flow Constraints',
      description:
        'Immediate payment needs drain liquidity, limiting flexibility for growth and operations.',
      icon: TrendingDown,
    },
    {
      title: 'Payment Delays',
      description:
        'Slow payment cycles stall critical transactions and disrupt business continuity.',
      icon: Clock,
    },
    {
      title: 'Manual Workflows',
      description: 'Legacy processes lead to errors, delays, and increased operational effort.',
      icon: AlertTriangle,
    },
    {
      title: 'Hidden Costs',
      description:
        'Traditional methods often include opaque fees, poor visibility, and inefficient use of capital.',
      icon: DollarSign,
    },
    {
      title: 'Supplier Friction',
      description:
        'Lack of payment flexibility weakens vendor relationships and reduces negotiation leverage.',
      icon: Users,
    },
    {
      title: 'Disconnected Systems',
      description:
        'Fragmented finance tools cause inefficiencies and limit real-time decision-making.',
      icon: Puzzle,
    },
    {
      title: 'Security Gaps',
      description: 'Inadequate controls expose payments to fraud, errors, and compliance risks.',
      icon: Shield,
    },
  ];

  return (
    <section className="section-spacing bg-gradient-to-br from-slate-50/80 via-red-50/40 to-orange-50/60 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-10 right-20 w-72 h-72 bg-gradient-to-br from-red-100/60 to-orange-100/60 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-64 h-64 bg-gradient-to-br from-purple-100/40 to-pink-100/40 rounded-full blur-3xl"></div>
      </div>

      {/* Subtle grid pattern overlay */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(51 65 85) 1px, transparent 0)`,
          backgroundSize: '24px 24px',
        }}
      ></div>

      {/* Aligned with Hero section margins for perfect consistency */}
      <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 w-full">
        <div className="w-full max-w-[98%] xl:max-w-[96%] mx-auto relative">
          {/* Main Header Section */}
          <div className="text-center mb-8 sm:mb-12 lg:mb-16">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4 sm:mb-6 leading-tight">
              Traditional B2B payouts fail —
              <span className="bg-gradient-to-r from-purple-600 via-blue-500 to-indigo-600 bg-clip-text text-transparent">
                {' '}
                and what we fix.
              </span>
            </h2>
          </div>

          {/* Problem Overview Header */}
          <motion.div
            className="text-center mb-8 sm:mb-12 lg:mb-16 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            {/* Problem Statement */}
            <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-8">
              <motion.div
                className="inline-flex items-center gap-2 bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full font-semibold text-sm shadow-lg"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <AlertTriangle className="w-4 h-4" />
                The Problem
              </motion.div>

              <motion.h3
                className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 leading-tight"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Legacy B2B payment systems create{' '}
                <span className="bg-gradient-to-r from-red-600 via-orange-500 to-red-600 bg-clip-text text-transparent">
                  critical bottlenecks
                </span>
              </motion.h3>

              <motion.p
                className="text-lg text-slate-600 leading-relaxed max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                Traditional payment methods drain liquidity, create operational friction, and limit
                business growth. These systemic issues compound over time, affecting everything from
                supplier relationships to cash flow management.
              </motion.p>
            </div>

            {/* Key Statistics */}
            <motion.div
              className="flex justify-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <div className="grid grid-cols-2 gap-4 sm:gap-6 max-w-md">
                <div className="bg-white border border-red-200 rounded-xl p-4 text-center shadow-sm">
                  <div className="text-2xl font-bold text-red-600">73%</div>
                  <div className="text-sm text-slate-600">Payment Delays</div>
                </div>
                <div className="bg-white border border-orange-200 rounded-xl p-4 text-center shadow-sm">
                  <div className="text-2xl font-bold text-orange-600">45%</div>
                  <div className="text-sm text-slate-600">Hidden Costs</div>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Enterprise-Grade 2x2+1 Card Grid */}
          <div className="space-y-6 sm:space-y-8">
            {/* First 6 cards in 2x2 grid (3 rows of 2 cards) */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              {problemCards.slice(0, 6).map((card, index) => (
                <motion.div
                  key={index}
                  className="group relative bg-white border border-slate-200/60 rounded-xl sm:rounded-2xl p-4 sm:p-5 lg:p-6 shadow-sm hover:shadow-xl hover:shadow-red-500/10 hover:border-red-300/50 transition-all duration-500 hover:-translate-y-1 backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-red-50/0 via-orange-50/0 to-red-50/0 group-hover:from-red-50/30 group-hover:via-orange-50/20 group-hover:to-red-50/30 rounded-xl sm:rounded-2xl transition-all duration-500 opacity-0 group-hover:opacity-100" />

                  <div className="relative flex flex-col h-full">
                    <div className="flex items-start gap-3 sm:gap-4 mb-3 sm:mb-4">
                      <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:from-red-100 group-hover:to-orange-100 group-hover:shadow-lg transition-all duration-500 group-hover:scale-110">
                        <card.icon className="w-5 h-5 sm:w-6 sm:h-6 text-slate-600 group-hover:text-red-600 transition-all duration-500" />
                      </div>
                      <h3 className="font-bold text-lg sm:text-xl text-slate-900 group-hover:text-red-700 transition-all duration-500 pt-1 leading-tight">
                        {card.title}
                      </h3>
                    </div>
                    <p className="text-slate-600 group-hover:text-slate-700 leading-relaxed text-sm sm:text-base transition-all duration-500">
                      {card.description}
                    </p>

                    {/* Subtle bottom accent line */}
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-500 via-orange-500 to-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-xl sm:rounded-b-2xl" />
                  </div>
                </motion.div>
              ))}
            </div>

            {/* 7th card centered below the grid */}
            {problemCards.length > 6 && (
              <div className="flex justify-center">
                <div className="w-full max-w-md md:max-w-lg">
                  <motion.div
                    className="group relative bg-white border border-slate-200/60 rounded-xl sm:rounded-2xl p-4 sm:p-5 lg:p-6 shadow-sm hover:shadow-xl hover:shadow-red-500/10 hover:border-red-300/50 transition-all duration-500 hover:-translate-y-1 backdrop-blur-sm"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    {/* Subtle gradient overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-br from-red-50/0 via-orange-50/0 to-red-50/0 group-hover:from-red-50/30 group-hover:via-orange-50/20 group-hover:to-red-50/30 rounded-xl sm:rounded-2xl transition-all duration-500 opacity-0 group-hover:opacity-100" />

                    <div className="relative flex flex-col h-full">
                      <div className="flex items-start gap-3 sm:gap-4 mb-3 sm:mb-4">
                        <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:from-red-100 group-hover:to-orange-100 group-hover:shadow-lg transition-all duration-500 group-hover:scale-110">
                          {(() => {
                            const IconComponent = problemCards[6].icon;
                            return (
                              <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 text-slate-600 group-hover:text-red-600 transition-all duration-500" />
                            );
                          })()}
                        </div>
                        <h3 className="font-bold text-lg sm:text-xl text-slate-900 group-hover:text-red-700 transition-all duration-500 pt-1 leading-tight">
                          {problemCards[6].title}
                        </h3>
                      </div>
                      <p className="text-slate-600 group-hover:text-slate-700 leading-relaxed text-sm sm:text-base transition-all duration-500">
                        {problemCards[6].description}
                      </p>

                      {/* Subtle bottom accent line */}
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-500 via-orange-500 to-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-xl sm:rounded-b-2xl" />
                    </div>
                  </motion.div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
