import { motion } from 'framer-motion';
import {
  AlertTriangle,
  Clock,
  DollarSign,
  Puzzle,
  Shield,
  TrendingDown,
  Users,
} from 'lucide-react';

export const LayoutSection = () => {
  const problemCards = [
    {
      title: 'Cash Flow Constraints',
      description:
        'Immediate payment needs drain liquidity, limiting flexibility for growth and operations.',
      icon: TrendingDown,
    },
    {
      title: 'Payment Delays',
      description:
        'Slow payment cycles stall critical transactions and disrupt business continuity.',
      icon: Clock,
    },
    {
      title: 'Manual Workflows',
      description: 'Legacy processes lead to errors, delays, and increased operational effort.',
      icon: AlertTriangle,
    },
    {
      title: 'Hidden Costs',
      description:
        'Traditional methods often include opaque fees, poor visibility, and inefficient use of capital.',
      icon: DollarSign,
    },
    {
      title: 'Supplier Friction',
      description:
        'Lack of payment flexibility weakens vendor relationships and reduces negotiation leverage.',
      icon: Users,
    },
    {
      title: 'Disconnected Systems',
      description:
        'Fragmented finance tools cause inefficiencies and limit real-time decision-making.',
      icon: Puzzle,
    },
    {
      title: 'Security Gaps',
      description: 'Inadequate controls expose payments to fraud, errors, and compliance risks.',
      icon: Shield,
    },
  ];

  return (
    <section className="section-spacing bg-gradient-to-br from-slate-50/80 via-red-50/40 to-orange-50/60 relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-10 right-20 w-72 h-72 bg-gradient-to-br from-red-100/60 to-orange-100/60 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-64 h-64 bg-gradient-to-br from-purple-100/40 to-pink-100/40 rounded-full blur-3xl"></div>
      </div>

      {/* Subtle grid pattern overlay */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(51 65 85) 1px, transparent 0)`,
          backgroundSize: '24px 24px',
        }}
      ></div>

      {/* Aligned with Hero section margins for perfect consistency */}
      <div className="max-w-none px-1 sm:px-2 lg:px-3 xl:px-4 w-full">
        <div className="w-full max-w-[98%] xl:max-w-[96%] mx-auto relative">
          {/* Header */}
          <div className="text-center mb-12 sm:mb-16 md:mb-20 lg:mb-24">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4 sm:mb-6 leading-tight">
              Traditional B2B payouts fail —
              <span className="bg-gradient-to-r from-purple-600 via-blue-500 to-indigo-600 bg-clip-text text-transparent">
                {' '}
                and what we fix.
              </span>
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 xl:gap-16 items-center mb-12 sm:mb-16 lg:mb-20">
            {/* Dynamic Problem Visualization */}
            <motion.div
              className="relative flex items-center justify-center order-2 lg:order-1"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              <div className="relative w-72 h-72 sm:w-80 sm:h-80 md:w-96 md:h-96">
                {/* Central Problem Core */}
                <motion.div
                  className="absolute inset-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-2xl border-4 border-white"
                  animate={{
                    scale: [1, 1.05, 1],
                    boxShadow: [
                      '0 25px 50px -12px rgba(239, 68, 68, 0.25)',
                      '0 25px 50px -12px rgba(239, 68, 68, 0.4)',
                      '0 25px 50px -12px rgba(239, 68, 68, 0.25)',
                    ],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                >
                  <div className="text-center">
                    <AlertTriangle className="w-8 h-8 text-white mx-auto mb-2" />
                    <p className="text-sm font-bold text-white">Legacy</p>
                    <p className="text-sm font-bold text-white">Issues</p>
                  </div>
                </motion.div>

                {/* Orbiting Problem Indicators */}
                {[0, 1, 2, 3, 4].map(index => {
                  const angle = (index * 72 + 36) * (Math.PI / 180); // 5 dots evenly spaced, offset to avoid center
                  const radius = 140; // Same radius as the rotating circle line
                  const x = Math.cos(angle) * radius + 192;
                  const y = Math.sin(angle) * radius + 192;

                  return (
                    <motion.div
                      key={index}
                      className="absolute w-6 h-6 bg-gradient-to-br from-orange-400 to-red-500 rounded-full shadow-lg"
                      style={{
                        left: x - 12,
                        top: y - 12,
                      }}
                      animate={{
                        scale: [1, 1.3, 1],
                        opacity: [0.7, 1, 0.7],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: index * 0.3,
                        ease: 'easeInOut',
                      }}
                    />
                  );
                })}

                {/* Rotating Problem Ring */}
                <motion.div
                  className="absolute inset-0"
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: 'linear',
                  }}
                >
                  <svg className="w-full h-full" viewBox="0 0 384 384">
                    <circle
                      cx="192"
                      cy="192"
                      r="140"
                      fill="none"
                      stroke="url(#problemGradient)"
                      strokeWidth="2"
                      strokeDasharray="10 5"
                      opacity="0.6"
                    />
                    <defs>
                      <linearGradient id="problemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#ef4444" />
                        <stop offset="100%" stopColor="#f97316" />
                      </linearGradient>
                    </defs>
                  </svg>
                </motion.div>

                {/* Problem Label */}
                <motion.div
                  className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-3 rounded-full font-bold text-xl shadow-lg"
                  initial={{ opacity: 0, y: -20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 1 }}
                >
                  The Problem
                </motion.div>
              </div>
            </motion.div>

            {/* Problem Overview Text */}
            <motion.div
              className="order-1 lg:order-2 space-y-4 sm:space-y-6 lg:space-y-8 px-2 sm:px-0"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              <div className="space-y-3 sm:space-y-4">
                <motion.h3
                  className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-slate-900 leading-tight"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  Legacy B2B payment systems create{' '}
                  <span className="bg-gradient-to-r from-red-600 via-orange-500 to-red-600 bg-clip-text text-transparent">
                    critical bottlenecks
                  </span>
                </motion.h3>

                <motion.p
                  className="text-base sm:text-lg text-slate-600 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  Traditional payment methods drain liquidity, create operational friction, and
                  limit business growth. These systemic issues compound over time, affecting
                  everything from supplier relationships to cash flow management.
                </motion.p>
              </div>

              {/* Key Statistics */}
              <motion.div
                className="grid grid-cols-2 gap-3 sm:gap-4 pt-2 sm:pt-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <div className="bg-white border border-red-200 rounded-lg sm:rounded-xl p-3 sm:p-4 text-center shadow-sm">
                  <div className="text-xl sm:text-2xl font-bold text-red-600">73%</div>
                  <div className="text-xs sm:text-sm text-slate-600">Payment Delays</div>
                </div>
                <div className="bg-white border border-orange-200 rounded-lg sm:rounded-xl p-3 sm:p-4 text-center shadow-sm">
                  <div className="text-xl sm:text-2xl font-bold text-orange-600">45%</div>
                  <div className="text-xs sm:text-sm text-slate-600">Hidden Costs</div>
                </div>
              </motion.div>
            </motion.div>
          </div>

          {/* Enterprise-Grade 2x2+1 Card Grid */}
          <div className="space-y-6 sm:space-y-8">
            {/* First 6 cards in 2x2 grid (3 rows of 2 cards) */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              {problemCards.slice(0, 6).map((card, index) => (
                <motion.div
                  key={index}
                  className="group relative bg-white border border-slate-200/60 rounded-xl sm:rounded-2xl p-4 sm:p-5 lg:p-6 shadow-sm hover:shadow-xl hover:shadow-red-500/10 hover:border-red-300/50 transition-all duration-500 hover:-translate-y-1 backdrop-blur-sm"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  {/* Subtle gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-red-50/0 via-orange-50/0 to-red-50/0 group-hover:from-red-50/30 group-hover:via-orange-50/20 group-hover:to-red-50/30 rounded-xl sm:rounded-2xl transition-all duration-500 opacity-0 group-hover:opacity-100" />

                  <div className="relative flex flex-col h-full">
                    <div className="flex items-start gap-3 sm:gap-4 mb-3 sm:mb-4">
                      <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:from-red-100 group-hover:to-orange-100 group-hover:shadow-lg transition-all duration-500 group-hover:scale-110">
                        <card.icon className="w-5 h-5 sm:w-6 sm:h-6 text-slate-600 group-hover:text-red-600 transition-all duration-500" />
                      </div>
                      <h3 className="font-bold text-lg sm:text-xl text-slate-900 group-hover:text-red-700 transition-all duration-500 pt-1 leading-tight">
                        {card.title}
                      </h3>
                    </div>
                    <p className="text-slate-600 group-hover:text-slate-700 leading-relaxed text-sm sm:text-base transition-all duration-500">
                      {card.description}
                    </p>

                    {/* Subtle bottom accent line */}
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-500 via-orange-500 to-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-xl sm:rounded-b-2xl" />
                  </div>
                </motion.div>
              ))}
            </div>

            {/* 7th card centered below the grid */}
            {problemCards.length > 6 && (
              <div className="flex justify-center">
                <div className="w-full max-w-md md:max-w-lg">
                  <motion.div
                    className="group relative bg-white border border-slate-200/60 rounded-xl sm:rounded-2xl p-4 sm:p-5 lg:p-6 shadow-sm hover:shadow-xl hover:shadow-red-500/10 hover:border-red-300/50 transition-all duration-500 hover:-translate-y-1 backdrop-blur-sm"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    {/* Subtle gradient overlay on hover */}
                    <div className="absolute inset-0 bg-gradient-to-br from-red-50/0 via-orange-50/0 to-red-50/0 group-hover:from-red-50/30 group-hover:via-orange-50/20 group-hover:to-red-50/30 rounded-xl sm:rounded-2xl transition-all duration-500 opacity-0 group-hover:opacity-100" />

                    <div className="relative flex flex-col h-full">
                      <div className="flex items-start gap-3 sm:gap-4 mb-3 sm:mb-4">
                        <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:from-red-100 group-hover:to-orange-100 group-hover:shadow-lg transition-all duration-500 group-hover:scale-110">
                          {(() => {
                            const IconComponent = problemCards[6].icon;
                            return (
                              <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 text-slate-600 group-hover:text-red-600 transition-all duration-500" />
                            );
                          })()}
                        </div>
                        <h3 className="font-bold text-lg sm:text-xl text-slate-900 group-hover:text-red-700 transition-all duration-500 pt-1 leading-tight">
                          {problemCards[6].title}
                        </h3>
                      </div>
                      <p className="text-slate-600 group-hover:text-slate-700 leading-relaxed text-sm sm:text-base transition-all duration-500">
                        {problemCards[6].description}
                      </p>

                      {/* Subtle bottom accent line */}
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-500 via-orange-500 to-red-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-xl sm:rounded-b-2xl" />
                    </div>
                  </motion.div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};
